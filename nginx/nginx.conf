user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # 优化的Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_min_length 1024;  # 只压缩大于1KB的文件
    gzip_proxied any;      # 压缩所有代理请求
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        text/html
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        application/x-javascript
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype
        image/svg+xml
        image/x-icon
        application/rss+xml
        application/atom+xml;

    include /etc/nginx/conf.d/*.conf;
} 