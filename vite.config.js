import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'

// https://vite.dev/config/
export default defineConfig({
  // 为 Node.js 模块提供浏览器兼容的空实现
  define: {
    global: 'globalThis',
    'process.env.NODE_ENV': '"development"',
    // 内网部署：确保不会尝试访问外部资源
    __INTRANET_MODE__: true,
    __DISABLE_EXTERNAL_RESOURCES__: true,
    __OFFLINE_MODE__: true,
    // 禁用segment analytics在浏览器环境中的使用
    'process.env.SEGMENT_ANALYTICS_DISABLED': '"true"',
    // React 兼容性定义
    '__DEV__': 'false'
    // 移除 __REACT_DEVTOOLS_GLOBAL_HOOK__ 定义，避免与浏览器扩展冲突
  },
  
  // 解析配置 - 排除Node.js专用包
  resolve: {
    alias: {
      // 为Node.js专用包提供空实现
      '@segment/analytics-node': 'virtual:segment-mock',
      'node-fetch': false,
    }
  },

  plugins: [
    react(),
    // Module Federation已移除 - 使用nginx代理方案替代
    // 这样可以更好地隐藏真实OpenVSCode地址并提供更好的安全性
    visualizer({ open: false, filename: 'logs/stats.html' }),
    // 替换segment imports插件
    {
      name: 'replace-segment-imports',
             generateBundle(options, bundle) {
         for (const [, chunk] of Object.entries(bundle)) {
           if (chunk.type === 'chunk' && chunk.code) {
             // 替换所有segment imports
             chunk.code = chunk.code
               .replace(/import\s*"@segment\/analytics-node"\s*;?/g, '/* @segment/analytics-node removed */')
               .replace(/import\s*['"]@segment\/analytics-node['"]\s*;?/g, '/* @segment/analytics-node removed */')
               .replace(/from\s*['"]@segment\/analytics-node['"]/g, 'from "data:text/javascript,export default {};"');
           }
         }
      }
    }
  ],
  server: {
    host: '0.0.0.0',
    port: 5173,
    strictPort: false,
    open: false
  },
  build: {
    // 优化构建性能和兼容性
    target: 'es2022', // Support top-level await for Module Federation
    cssTarget: 'chrome61',
    minify: 'terser', // 启用Terser压缩，提供更好的压缩率
    cssMinify: 'esbuild', // CSS 使用 esbuild 压缩
    modulePreload: {
      polyfill: true
    },
    sourcemap: false, // 生产环境关闭 sourcemap
    reportCompressedSize: process.env.BUILD_VERBOSE === 'true', // 可选择性显示压缩大小详情

    // Terser 压缩配置
    terserOptions: {
      compress: {
        drop_console: true, // 移除 console
        drop_debugger: true, // 移除 debugger
        pure_funcs: ['console.log', 'console.info', 'console.debug'], // 移除指定函数调用
        passes: 2 // 多次压缩以获得更好效果
      },
      mangle: {
        safari10: true // 兼容 Safari 10
      },
      format: {
        comments: false // 移除注释
      }
    },

    rollupOptions: {
      // 外部化 Node.js 模块，避免浏览器兼容性警告  
      external: ['@segment/analytics-node', 'node-fetch'],

      output: {
        // 极致优化的代码分割策略 - 最大化并行加载
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            // React 生态系统细分
            if (id.includes('react-dom/client')) return 'vendor-react-dom-client';
            if (id.includes('react-dom')) return 'vendor-react-dom-core';
            if (id.includes('react/jsx-runtime')) return 'vendor-react-jsx';
            if (id.includes('react/') && !id.includes('react-')) return 'vendor-react-core';
            if (id.includes('scheduler')) return 'vendor-react-scheduler';

            // 图标库按类型分割
            if (id.includes('react-icons/fa')) return 'vendor-icons-fa';
            if (id.includes('react-icons/hi')) return 'vendor-icons-hi';
            if (id.includes('react-icons')) return 'vendor-icons-other';

            // 语法高亮库细分
            if (id.includes('highlight.js/es/core')) return 'vendor-highlight-core';
            if (id.includes('highlight.js/es/languages')) return 'vendor-highlight-langs';
            if (id.includes('highlight.js')) return 'vendor-highlight-utils';

            // Markdown处理库细分
            if (id.includes('react-markdown')) return 'vendor-markdown-react';
            if (id.includes('remark-parse') || id.includes('remark-gfm')) return 'vendor-markdown-remark';
            if (id.includes('rehype-highlight') || id.includes('rehype-raw')) return 'vendor-markdown-rehype';
            if (id.includes('unified') || id.includes('micromark')) return 'vendor-markdown-unified';
            if (id.includes('mdast') || id.includes('hast')) return 'vendor-markdown-ast';

            // HTTP 和网络库
            if (id.includes('axios')) return 'vendor-http-axios';

            // AI SDK 分离
            if (id.includes('openai')) return 'vendor-ai-openai';
            if (id.includes('anthropic')) return 'vendor-ai-anthropic';

            // 工具库细分
            if (id.includes('lodash')) return 'vendor-utils-lodash';
            if (id.includes('date-fns')) return 'vendor-utils-date';

            // 其他常用库
            if (id.includes('express') || id.includes('cors')) return 'vendor-server';
            if (id.includes('ldapjs')) return 'vendor-ldap';

            // 剩余的小型库
            return 'vendor-misc';
          }

          // 组件代码按功能模块分割
          if (id.includes('src/components')) {
            if (id.includes('DocumentationSection')) return 'component-docs';
            if (id.includes('DownloadsSection')) return 'component-downloads';
            if (id.includes('WebIDESection')) return 'component-webide';
            if (id.includes('ChatSection')) return 'component-chat';
            if (id.includes('NewsSection')) return 'component-news';
            if (id.includes('Navbar') || id.includes('Footer')) return 'component-layout';
            if (id.includes('Modal') || id.includes('Dialog')) return 'component-ui';
            return 'components-common';
          }

          // 服务层细分
          if (id.includes('src/services')) {
            if (id.includes('documentService')) return 'service-docs';
            if (id.includes('authManager')) return 'service-auth';
            if (id.includes('apiService')) return 'service-api';
            return 'services-misc';
          }

          // 工具类细分
          if (id.includes('src/utils')) {
            if (id.includes('browserCompatibility') || id.includes('browserPerformanceMonitor')) return 'utils-browser';
            if (id.includes('smartDetectionStrategy')) return 'utils-detection';
            return 'utils-misc';
          }
        },
        
        // 文件命名优化
        chunkFileNames: () => {
          return `assets/[name]-[hash].js`;
        },
        
        assetFileNames: (assetInfo) => {
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `assets/images/[name]-[hash][extname]`;
          }
          if (/\.(css)$/i.test(assetInfo.name)) {
            return `assets/css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    
    // 提升chunk大小警告阈值，适应现代应用需求
    chunkSizeWarningLimit: 1500, // 提高到 1.5MB，现代应用的合理阈值
    
    // 资源内联阈值
    assetsInlineLimit: 4096
  },
  
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      // 只预构建实际使用的图标包
      'react-icons/fa',
      'react-icons/hi',
      'axios'
    ],
    exclude: [
      'openai', // 后端专用
      '@anthropic-ai/sdk', // 后端专用
      'express', // 后端专用
      'cors', // 后端专用
      'ldapjs', // 后端专用
      'node-fetch', // 后端专用
      'dotenv', // 后端专用
      'uuid', // 按需导入
      'concurrently', // 开发工具
      'react-router-dom', // 当前未使用
      '@segment/analytics-node', // Node.js 专用，避免浏览器构建警告
      '@segment/analytics-next',
      // 排除不常用的语法高亮语言包
      'prismjs/components',
      'highlight.js/lib/languages'
    ]
  },
  
  // CSS优化
  css: {
    devSourcemap: false,
    preprocessorOptions: {
      scss: {
        charset: false
      }
    },
    // 生产环境CSS优化 - 移动到postcss.config.js文件中处理
  },

  // 静态资源处理 - 支持现代图片格式
  assetsInclude: ['**/*.webp', '**/*.avif', '**/*.jpg', '**/*.jpeg', '**/*.png'],
  
  // 性能优化
  esbuild: {
    drop: ['console', 'debugger'],
    legalComments: 'none'
  }
})
