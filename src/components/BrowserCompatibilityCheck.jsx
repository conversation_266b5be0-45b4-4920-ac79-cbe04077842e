import React, { useState, useEffect, useCallback } from 'react';
import { getRecommendedBrowsers } from '../utils/browserCompatibility';

// 浏览器下载网格组件
const BrowserDownloadGrid = () => {
  const browsers = getRecommendedBrowsers();

  const handleBrowserClick = (browser) => {
    // 如果有本地下载链接，直接下载
    if (browser.localDownloadUrl) {
      const link = document.createElement('a');
      link.href = browser.localDownloadUrl;
      link.download = browser.localDownloadUrl.split('/').pop();
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 显示下载提示
      alert(`正在下载 ${browser.name}...\n\n下载完成后请运行安装程序并按照提示完成安装。`);
    } else {
      // 显示详细的获取指南
      const message = `
获取 ${browser.name} 的方式：

1. 官方网站：${browser.downloadUrl || '请访问官方网站'}
2. 内网环境：请联系IT部门获取安装包
3. 自动更新：在现有浏览器中检查更新

推荐版本：${browser.minVersion} 或更高版本
      `.trim();

      alert(message);
    }
  };

  const getBrowserUpdateInstructions = (browserName) => {
    const instructions = {
      'Google Chrome': [
        '点击浏览器右上角的三个点菜单',
        '选择"帮助" > "关于 Google Chrome"',
        '浏览器会自动检查并下载更新'
      ],
      'Mozilla Firefox': [
        '点击浏览器右上角的菜单按钮',
        '选择"帮助" > "关于 Firefox"',
        '浏览器会自动检查并下载更新'
      ],
      'Microsoft Edge': [
        '点击浏览器右上角的三个点菜单',
        '选择"帮助和反馈" > "关于 Microsoft Edge"',
        '浏览器会自动检查并下载更新'
      ],
      'Safari': [
        '在 macOS 上，打开"系统偏好设置"',
        '点击"软件更新"',
        'Safari 会随系统更新一起更新'
      ]
    };
    return instructions[browserName] || ['请访问浏览器官方网站获取更新指南'];
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {browsers.map((browser, index) => (
          <div
            key={index}
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center gap-3 mb-3">
              <div className="text-2xl">{browser.icon}</div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  {browser.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {browser.description}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  推荐版本: {browser.minVersion}+
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <button
                onClick={() => handleBrowserClick(browser)}
                className={`w-full px-3 py-2 text-white text-sm rounded-lg transition-colors flex items-center justify-center gap-2 ${
                  browser.localDownloadUrl
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                <i className={`fas ${browser.localDownloadUrl ? 'fa-download' : 'fa-external-link-alt'}`}></i>
                {browser.localDownloadUrl ? '直接下载' : '获取安装包'}
              </button>

              <details className="text-xs text-gray-600 dark:text-gray-400">
                <summary className="cursor-pointer hover:text-gray-800 dark:hover:text-gray-200">
                  查看更新指南
                </summary>
                <div className="mt-2 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
                  <ol className="list-decimal list-inside space-y-1">
                    {getBrowserUpdateInstructions(browser.name).map((step, stepIndex) => (
                      <li key={stepIndex}>{step}</li>
                    ))}
                  </ol>
                </div>
              </details>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
          <i className="fas fa-info-circle mr-2 text-blue-600"></i>
          内网环境说明
        </h4>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          由于安全策略，本平台运行在内网环境中。如需升级浏览器，请联系您的IT管理员获取相应的安装包。
          IT管理员可以从各浏览器官方网站下载企业版安装包进行部署。
        </p>
      </div>
    </div>
  );
};

const BrowserCompatibilityCheck = () => {
  const [compatibility, setCompatibility] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [showUpgradeInterface, setShowUpgradeInterface] = useState(false);
  const [notificationType, setNotificationType] = useState('warning'); // 'error', 'warning', 'info'
  const [resourceIssues, setResourceIssues] = useState([]);
  const [hasResourceProblems, setHasResourceProblems] = useState(false);

  // 监听来自 PixelLoader 的兼容性检测事件 - 兼容旧浏览器
  useEffect(() => {
    const handleCompatibilityCheck = (event) => {
      try {
        const result = event.detail || event.data || {}; // 兼容不同的事件格式
        console.log('[BrowserCompatibilityCheck] 收到兼容性检测结果:', result);

        setCompatibility(result);

        // 安全检查结果对象
        const isCompatible = result.isCompatible !== false;
        const isRecommendedVersion = result.isRecommendedVersion !== false;
        const integrationIssues = (result.issues && result.issues.integrationIssues) || [];

        console.log('[BrowserCompatibilityCheck] 兼容性状态:', {
          isCompatible,
          isRecommendedVersion,
          integrationIssues: integrationIssues.length,
          browserName: result.browser?.name
        });

        // 根据兼容性结果决定是否显示通知
        if (!isCompatible) {
          // 特别处理IE浏览器
          if (result.browser && result.browser.name === 'Internet Explorer') {
            setNotificationType('error');
            setIsVisible(true);
            console.log('[BrowserCompatibilityCheck] 显示IE错误通知');
          } else {
            setNotificationType('error');
            setIsVisible(true);
            console.log('[BrowserCompatibilityCheck] 显示兼容性错误通知');
          }
        } else if (integrationIssues.length > 0) {
          // 只有集成问题时才显示警告，不再因为推荐版本问题显示警告
          setNotificationType('warning');
          setIsVisible(true);
          console.log('[BrowserCompatibilityCheck] 显示集成问题警告通知');
        } else if (!isRecommendedVersion && result.browser?.version < 80) {
          // 只有当浏览器版本确实很旧时（小于80）才因为推荐版本问题显示警告
          setNotificationType('warning');
          setIsVisible(true);
          console.log('[BrowserCompatibilityCheck] 显示旧版本警告通知');
        } else {
          console.log('[BrowserCompatibilityCheck] 浏览器兼容性良好，不显示通知');
        }
      } catch (error) {
        console.warn('处理兼容性检测结果时出错:', error);
        // 在错误情况下，显示一个通用的提醒
        setNotificationType('info');
        setIsVisible(true);
      }
    };

    // 兼容旧浏览器的事件监听
    if (typeof window !== 'undefined') {
      if (window.addEventListener) {
        // 现代浏览器
        window.addEventListener('browserCompatibilityChecked', handleCompatibilityCheck);
      } else if (window.attachEvent) {
        // IE8 及更早版本
        window.attachEvent('onbrowserCompatibilityChecked', handleCompatibilityCheck);
      }
    }

    // 清理函数 - 兼容旧浏览器
    return () => {
      if (typeof window !== 'undefined') {
        if (window.removeEventListener) {
          window.removeEventListener('browserCompatibilityChecked', handleCompatibilityCheck);
        } else if (window.detachEvent) {
          window.detachEvent('onbrowserCompatibilityChecked', handleCompatibilityCheck);
        }
      }
    };
  }, []);

  // 监听资源兼容性问题
  useEffect(() => {
    const handleResourceIssue = (event) => {
      try {
        const { issue, allIssues, summary } = event.detail;
        console.log('[BrowserCompatibilityCheck] 收到资源兼容性问题:', {
          issue,
          totalIssues: allIssues.length,
          summary
        });

        setResourceIssues(allIssues);
        setHasResourceProblems(true);

        // 如果是严重问题，显示通知
        if (summary.status === 'critical') {
          setNotificationType('error');
          setIsVisible(true);
          console.log('[BrowserCompatibilityCheck] 显示资源错误通知');
        } else if (summary.status === 'warning') {
          setNotificationType('warning');
          setIsVisible(true);
          console.log('[BrowserCompatibilityCheck] 显示资源警告通知');
        }

        console.log('[BrowserCompatibilityCheck] 资源兼容性问题详情:', issue);
      } catch (error) {
        console.warn('处理资源兼容性问题时出错:', error);
      }
    };

    // 监听资源兼容性问题事件
    window.addEventListener('resourceCompatibilityIssue', handleResourceIssue);

    return () => {
      window.removeEventListener('resourceCompatibilityIssue', handleResourceIssue);
    };
  }, []);

  const toggleDetails = useCallback(() => {
    setShowDetails(prev => !prev);
  }, []);

  const handleDismiss = useCallback(() => {
    setIsVisible(false);
  }, []);

  const handleShowUpgrade = useCallback(() => {
    setShowUpgradeInterface(true);
  }, []);

  const handleCloseUpgrade = useCallback(() => {
    setShowUpgradeInterface(false);
  }, []);

  // 获取通知样式
  const getNotificationStyles = () => {
    switch (notificationType) {
      case 'error':
        return {
          bgColor: 'bg-red-500/90',
          borderColor: 'border-red-400',
          textColor: 'text-white',
          icon: 'fas fa-exclamation-circle'
        };
      case 'warning':
        return {
          bgColor: 'bg-yellow-500/90',
          borderColor: 'border-yellow-400',
          textColor: 'text-black',
          icon: 'fas fa-exclamation-triangle'
        };
      case 'info':
        return {
          bgColor: 'bg-blue-500/90',
          borderColor: 'border-blue-400',
          textColor: 'text-white',
          icon: 'fas fa-info-circle'
        };
      default:
        return {
          bgColor: 'bg-yellow-500/90',
          borderColor: 'border-yellow-400',
          textColor: 'text-black',
          icon: 'fas fa-exclamation-triangle'
        };
    }
  };

  if (!compatibility || !isVisible) {
    return null;
  }

  const styles = getNotificationStyles();

  return (
    <>
      {/* 主通知 - 增强可访问性 */}
      {isVisible && (
        <div
          className="fixed top-4 right-4 z-50 max-w-md animate-fade-in-up"
          role="alert"
          aria-live="polite"
          aria-atomic="true"
        >
        <div className={`${styles.bgColor} backdrop-blur-sm ${styles.textColor} p-4 rounded-lg shadow-lg border ${styles.borderColor}`}>
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center gap-2">
              <i className={`${styles.icon} text-lg`} aria-hidden="true"></i>
              <span className="font-semibold">
                {notificationType === 'error' ? '浏览器兼容性问题' :
                 notificationType === 'warning' ? '浏览器兼容性提醒' :
                 '浏览器信息'}
              </span>
            </div>
            <button
              onClick={handleDismiss}
              className={`${styles.textColor}/60 hover:${styles.textColor} text-xl leading-none p-1 rounded focus:outline-none focus:ring-2 focus:ring-white/50`}
              aria-label="关闭浏览器兼容性通知"
              title="关闭"
            >
              ×
            </button>
          </div>

          <div className="text-sm mb-3">
            {notificationType === 'error' ? (
              compatibility && compatibility.browser && compatibility.browser.name === 'Internet Explorer' ? (
                <div>
                  <p className="font-semibold mb-2">⚠️ 检测到您正在使用Internet Explorer</p>
                  <p>IE浏览器已停止支持，无法运行本平台。为了您的安全和最佳体验，请立即升级到现代浏览器。</p>
                </div>
              ) : hasResourceProblems ? (
                <div>
                  <p className="font-semibold mb-2">🔧 检测到资源加载问题</p>
                  <p>页面中的某些图标或样式可能无法正常显示，这通常是由于浏览器兼容性问题导致的。</p>
                  {resourceIssues.length > 0 && (
                    <div className="mt-2 text-xs">
                      <p>主要问题：</p>
                      <ul className="list-disc list-inside mt-1">
                        {resourceIssues.slice(0, 3).map((issue, index) => (
                          <li key={index}>{issue.message}</li>
                        ))}
                        {resourceIssues.length > 3 && (
                          <li>还有 {resourceIssues.length - 3} 个其他问题...</li>
                        )}
                      </ul>
                    </div>
                  )}
                </div>
              ) : (
                <p>您的浏览器版本过旧，可能无法正常使用本平台的功能。</p>
              )
            ) : (
              <div>
                <p>检测到您的浏览器可能不完全支持本平台的所有功能。</p>
                {hasResourceProblems && (
                  <p className="mt-1 text-xs">部分图标或样式可能显示异常。</p>
                )}
              </div>
            )}
          </div>

          <div className="flex items-center gap-2 mb-3" role="group" aria-label="浏览器兼容性操作">
              {(notificationType === 'error' || notificationType === 'warning') && (
                <button
                  onClick={handleShowUpgrade}
                  className={`text-sm bg-white/20 px-3 py-2 rounded hover:bg-white/30 transition-colors ${styles.textColor} font-medium focus:outline-none focus:ring-2 focus:ring-white/50`}
                  aria-label="查看浏览器升级选项"
                  title="获取浏览器升级帮助"
                >
                  <i className="fas fa-arrow-up mr-1" aria-hidden="true"></i>
                  升级浏览器
                </button>
              )}
              <button
                onClick={toggleDetails}
                className={`text-sm bg-white/20 px-3 py-2 rounded hover:bg-white/30 transition-colors ${styles.textColor} focus:outline-none focus:ring-2 focus:ring-white/50`}
                aria-label={showDetails ? '隐藏兼容性详细信息' : '查看兼容性详细信息'}
                aria-expanded={showDetails}
                aria-controls="compatibility-details"
              >
                <i className={`fas ${showDetails ? 'fa-eye-slash' : 'fa-eye'} mr-1`} aria-hidden="true"></i>
                {showDetails ? '隐藏详情' : '查看详情'}
              </button>
              <button
                onClick={handleDismiss}
                className={`text-sm bg-white/20 px-3 py-2 rounded hover:bg-white/30 transition-colors ${styles.textColor} focus:outline-none focus:ring-2 focus:ring-white/50`}
                aria-label="关闭兼容性通知"
              >
                <i className="fas fa-check mr-1" aria-hidden="true"></i>
                我知道了
              </button>
            </div>

            {showDetails && (
              <div
                id="compatibility-details"
                className="bg-white/10 rounded p-3 mb-3 text-xs"
                role="region"
                aria-label="浏览器兼容性详细信息"
              >
                <div className="grid grid-cols-1 gap-2">
                  <div>
                    <strong>浏览器:</strong> {compatibility?.browser?.name || '未知'} {compatibility?.browser?.fullVersion || ''}
                  </div>
                  <div>
                    <strong>平台:</strong> {compatibility?.browser?.platform || '未知'}
                  </div>
                  <div>
                    <strong>引擎:</strong> {compatibility?.browser?.engine || '未知'}
                  </div>
                  
                  {compatibility?.issues && (
                    <div className="mt-2">
                      <strong>兼容性问题:</strong>
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        {compatibility.issues.versionTooOld && (
                          <li className="text-red-800">
                            <i className="fas fa-times-circle mr-1"></i>浏览器版本过旧
                          </li>
                        )}
                        {compatibility.issues.unsupportedFeatures?.length > 0 && (
                          <li className="text-red-800">
                            <i className="fas fa-times-circle mr-1"></i>缺少关键特性: {compatibility.issues.unsupportedFeatures.join(', ')}
                          </li>
                        )}
                        {compatibility.issues.missingRecommendedFeatures?.length > 0 && (
                          <li className="text-yellow-800">
                            <i className="fas fa-exclamation-triangle mr-1"></i>缺少推荐特性: {compatibility.issues.missingRecommendedFeatures.join(', ')}
                          </li>
                        )}
                      </ul>
                    </div>
                  )}

                  <div className="mt-2">
                    <strong>关键特性支持:</strong>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      {[
                        { key: 'es6Support', name: 'ES6 语法' },
                        { key: 'fetchAPI', name: 'Fetch API' },
                        { key: 'promiseSupport', name: 'Promise' },
                        { key: 'localStorage', name: '本地存储' },
                        { key: 'cssFlexbox', name: 'CSS Flexbox' },
                        { key: 'cssGrid', name: 'CSS Grid' },
                        { key: 'serviceWorker', name: 'Service Worker' }
                      ].map(({ key, name }) => {
                        const isSupported = compatibility?.features?.[key] ?? false;
                        return (
                          <li key={key} className={isSupported ? 'text-green-800' : 'text-red-800'}>
                            <i className={`${isSupported ? 'fas fa-check-circle' : 'fas fa-times-circle'} mr-1`}></i>
                            {name}
                            {compatibility?.features === undefined && (
                              <span className="text-gray-500 text-xs ml-1">(检测中...)</span>
                            )}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            <div className="text-xs">
              <p className="mb-2">建议使用现代浏览器以获得最佳体验：</p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-white/20 px-2 py-1 rounded">Chrome 60+</span>
                <span className="bg-white/20 px-2 py-1 rounded">Firefox 60+</span>
                <span className="bg-white/20 px-2 py-1 rounded">Safari 12+</span>
                <span className="bg-white/20 px-2 py-1 rounded">Edge 79+</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 浏览器升级界面 - 增强可访问性 */}
      {showUpgradeInterface && (
        <div
          className="fixed inset-0 z-60 flex items-center justify-center bg-black/50 backdrop-blur-sm"
          role="dialog"
          aria-modal="true"
          aria-labelledby="upgrade-dialog-title"
          aria-describedby="upgrade-dialog-description"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2
                  id="upgrade-dialog-title"
                  className="text-xl font-bold text-gray-900 dark:text-white"
                >
                  <i className="fas fa-arrow-up mr-2 text-blue-600"></i>
                  升级您的浏览器
                </h2>
                <button
                  onClick={handleCloseUpgrade}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-2xl p-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  aria-label="关闭升级对话框"
                  title="关闭"
                >
                  ×
                </button>
              </div>

              <div className="mb-6">
                <p
                  id="upgrade-dialog-description"
                  className="text-gray-600 dark:text-gray-300 mb-4 text-base leading-relaxed"
                >
                  为了确保您能够正常使用本平台的所有功能，我们建议您升级到以下浏览器的最新版本。
                  新版本浏览器不仅更安全，还能提供更好的性能和用户体验。
                </p>

                {compatibility && (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <i className="fas fa-info-circle text-yellow-600 dark:text-yellow-400"></i>
                      <span className="font-medium text-yellow-800 dark:text-yellow-200">当前浏览器信息</span>
                    </div>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                      {compatibility?.browser?.name || '未知浏览器'} {compatibility?.browser?.fullVersion || ''} ({compatibility?.browser?.platform || '未知平台'})
                    </p>
                  </div>
                )}
              </div>

              <BrowserDownloadGrid />

              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-3">
                  <i className="fas fa-lightbulb mr-2" aria-hidden="true"></i>
                  为什么要升级浏览器？
                </h3>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-2" role="list">
                  <li className="flex items-start gap-2">
                    <i className="fas fa-shield-alt mt-0.5 text-blue-600" aria-hidden="true"></i>
                    <span><strong>更高安全性：</strong>新版本修复了已知的安全漏洞，保护您的数据安全</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <i className="fas fa-rocket mt-0.5 text-blue-600" aria-hidden="true"></i>
                    <span><strong>更好性能：</strong>优化的渲染引擎让网页加载更快，运行更流畅</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <i className="fas fa-star mt-0.5 text-blue-600" aria-hidden="true"></i>
                    <span><strong>新功能支持：</strong>支持最新的网页技术，享受更丰富的功能体验</span>
                  </li>
                </ul>
              </div>

              <div className="mt-6 flex justify-end gap-3">
                <button
                  onClick={handleCloseUpgrade}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                  aria-label="稍后再升级浏览器"
                >
                  稍后再说
                </button>
                <button
                  onClick={() => {
                    handleCloseUpgrade();
                    handleDismiss();
                  }}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  aria-label="确认已了解浏览器升级信息"
                >
                  我知道了
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BrowserCompatibilityCheck;