import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { FaClock, FaInfoCircle } from 'react-icons/fa';
import authManager from '../services/authManager';

const UserStatusIndicator = React.memo(({ user }) => {
  const [loginStatus, setLoginStatus] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  // 使用useCallback优化状态更新函数
  const updateLoginStatus = useCallback(() => {
    if (!user) {
      setLoginStatus(null);
      return;
    }
    const status = authManager.getLoginStatus();
    setLoginStatus(status);
  }, [user]);

  useEffect(() => {
    if (!user) {
      setLoginStatus(null);
      return;
    }

    // 获取初始状态
    updateLoginStatus();

    // 每5分钟更新一次状态（原来是1分钟，减少频率）
    const timer = setInterval(updateLoginStatus, 5 * 60 * 1000);

    return () => clearInterval(timer);
  }, [user, updateLoginStatus]);

  // 使用useMemo优化过期状态计算
  const isExpiringSoon = useMemo(() => {
    if (!loginStatus?.remaining || loginStatus.remaining.expired) return false;
    return loginStatus.remaining.remaining < 4 * 60 * 60 * 1000; // 小于4小时
  }, [loginStatus]);

  // 使用useCallback优化点击处理
  const toggleDetails = useCallback(() => {
    setShowDetails(prev => !prev);
  }, []);

  // 使用useCallback优化刷新登录时间处理
  const handleRefreshLogin = useCallback(() => {
    if (authManager.refreshLoginTime()) {
      updateLoginStatus();
      setShowDetails(false);
    }
  }, [updateLoginStatus]);

  // 将条件检查移到hooks之后，避免hooks规则违反
  if (!user || !loginStatus || !loginStatus.isLoggedIn) {
    return null;
  }

  const { remaining } = loginStatus;

  return (
    <div
      className="relative"
    >
      <div 
        className={`flex items-center gap-2 px-3 py-1 rounded-full text-xs cursor-pointer transition-all ${
          isExpiringSoon 
            ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30' 
            : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
        }`}
        onClick={toggleDetails}
      >
        <FaClock size={10} />
        <span>
          {remaining && !remaining.expired 
            ? `${remaining.days || ''}${remaining.days ? '天' : ''}${remaining.hours || ''}${remaining.hours ? '小时' : ''}${!remaining.days && !remaining.hours ? '即将过期' : ''}` 
            : '已过期'
          }
        </span>
        <FaInfoCircle size={10} className="opacity-60" />
      </div>

      {/* 详细信息悬浮窗 */}
      {showDetails ? (
        <div
          className="absolute top-full right-0 mt-2 w-64 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50"
        >
          <div className="p-4">
            <h4 className="text-sm font-semibold text-white mb-3">登录状态详情</h4>
            
            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-400">登录时间:</span>
                <span className="text-white">
                  {loginStatus.loginTime ? loginStatus.loginTime.toLocaleString() : '未知'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-400">有效期:</span>
                <span className="text-white">{loginStatus.timeoutDays}天</span>
              </div>
              
              {remaining && !remaining.expired ? (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-400">剩余时间:</span>
                    <span className={`font-medium ${isExpiringSoon ? 'text-yellow-400' : 'text-green-400'}`}>
                      {remaining.formattedTime}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-400">过期时间:</span>
                    <span className="text-white">
                      {new Date(loginStatus.loginTime.getTime() + loginStatus.timeoutDays * 24 * 60 * 60 * 1000).toLocaleString()}
                    </span>
                  </div>
                </>
              ) : null}
              
              {isExpiringSoon ? (
                <div className="mt-3 p-2 bg-yellow-500/10 border border-yellow-500/30 rounded text-yellow-400">
                  <div className="flex items-center gap-1">
                    <FaInfoCircle size={10} />
                    <span>登录即将过期，请及时重新登录</span>
                  </div>
                </div>
              ) : null}
            </div>
            
            <div className="mt-3 pt-3 border-t border-gray-700">
              <button
                onClick={handleRefreshLogin}
                className="w-full text-xs bg-cyan-500/20 hover:bg-cyan-500/30 text-cyan-400 py-2 rounded transition-colors"
              >
                刷新登录时间
              </button>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
});

UserStatusIndicator.displayName = 'UserStatusIndicator';

export default UserStatusIndicator;