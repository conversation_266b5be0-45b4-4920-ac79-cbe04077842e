import React, { useState, useEffect } from 'react';
import { 
  getDetectionStatistics, 
  clearDetectionCache, 
  enablePerformanceMonitoring, 
  disablePerformanceMonitoring,
  forceCheckBrowserCompatibility 
} from '../utils/browserCompatibility';
import browserPerformanceMonitor from '../utils/browserPerformanceMonitor';

/**
 * 浏览器兼容性管理面板
 * 用于监控和管理浏览器兼容性检测系统
 */
const BrowserCompatibilityDashboard = ({ isVisible, onClose }) => {
  const [statistics, setStatistics] = useState(null);
  const [performanceReport, setPerformanceReport] = useState(null);
  const [isMonitoringEnabled, setIsMonitoringEnabled] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isVisible) {
      loadData();
      checkMonitoringStatus();
    }
  }, [isVisible]);

  const loadData = async () => {
    setLoading(true);
    try {
      const stats = await getDetectionStatistics();
      const report = browserPerformanceMonitor ? browserPerformanceMonitor.getReport() : null;
      setStatistics(stats);
      setPerformanceReport(report);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkMonitoringStatus = () => {
    const enabled = localStorage.getItem('browser-monitoring-enabled') === 'true';
    setIsMonitoringEnabled(enabled);
  };

  const handleToggleMonitoring = async () => {
    if (isMonitoringEnabled) {
      await disablePerformanceMonitoring();
      setIsMonitoringEnabled(false);
    } else {
      await enablePerformanceMonitoring();
      setIsMonitoringEnabled(true);
    }
  };

  const handleClearCache = async () => {
    if (confirm('确定要清除所有检测缓存吗？这将导致下次访问时重新进行完整检测。')) {
      await clearDetectionCache();
      loadData();
      alert('缓存已清除');
    }
  };

  const handleClearMonitoringData = () => {
    if (confirm('确定要清除所有监控数据吗？这个操作不可恢复。')) {
      browserPerformanceMonitor.constructor.clearData();
      loadData();
      alert('监控数据已清除');
    }
  };

  const handleForceCheck = async () => {
    setLoading(true);
    try {
      const result = forceCheckBrowserCompatibility();
      console.log('Force check result:', result);
      loadData();
      alert('强制检测完成，请查看控制台了解详细结果');
    } catch (error) {
      console.error('Force check failed:', error);
      alert('强制检测失败：' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
        <div className="p-6">
          {/* 头部 */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">浏览器兼容性管理面板</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-2xl"
              aria-label="关闭"
            >
              ×
            </button>
          </div>

          {loading && (
            <div className="text-center py-4">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          )}

          {/* 控制面板 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <button
              onClick={handleToggleMonitoring}
              className={`p-4 rounded-lg text-white font-medium ${
                isMonitoringEnabled 
                  ? 'bg-green-600 hover:bg-green-700' 
                  : 'bg-gray-600 hover:bg-gray-700'
              }`}
            >
              {isMonitoringEnabled ? '禁用监控' : '启用监控'}
            </button>

            <button
              onClick={handleForceCheck}
              disabled={loading}
              className="p-4 rounded-lg bg-blue-600 hover:bg-blue-700 text-white font-medium disabled:opacity-50"
            >
              强制检测
            </button>

            <button
              onClick={handleClearCache}
              className="p-4 rounded-lg bg-yellow-600 hover:bg-yellow-700 text-white font-medium"
            >
              清除缓存
            </button>

            <button
              onClick={handleClearMonitoringData}
              className="p-4 rounded-lg bg-red-600 hover:bg-red-700 text-white font-medium"
            >
              清除数据
            </button>
          </div>

          {/* 统计信息 */}
          {statistics && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4">检测统计</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {(statistics.cacheHitRate * 100).toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600">缓存命中率</div>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {statistics.avgDetectionTime.toFixed(0)}ms
                  </div>
                  <div className="text-sm text-gray-600">平均检测时间</div>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {statistics.totalDetections}
                  </div>
                  <div className="text-sm text-gray-600">总检测次数</div>
                </div>

                <div className="bg-orange-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    {statistics.recentDetections}
                  </div>
                  <div className="text-sm text-gray-600">近7天检测</div>
                </div>
              </div>
            </div>
          )}

          {/* 浏览器分布 */}
          {statistics?.browserDistribution && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4">浏览器分布</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                {Object.entries(statistics.browserDistribution).map(([browser, count]) => (
                  <div key={browser} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                    <span className="font-medium">{browser}</span>
                    <span className="text-gray-600">{count} 次</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 性能报告 */}
          {performanceReport && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4">性能报告</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">兼容性统计</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>兼容:</span>
                      <span className="text-green-600">{performanceReport.compatibility?.compatible || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>警告:</span>
                      <span className="text-yellow-600">{performanceReport.compatibility?.warnings || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>不兼容:</span>
                      <span className="text-red-600">{performanceReport.compatibility?.incompatible || 0}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">错误统计</h4>
                  <div className="space-y-2">
                    {Object.entries(performanceReport.errors || {}).map(([source, count]) => (
                      <div key={source} className="flex justify-between">
                        <span>{source}:</span>
                        <span className="text-red-600">{count}</span>
                      </div>
                    ))}
                    {Object.keys(performanceReport.errors || {}).length === 0 && (
                      <div className="text-gray-500">无错误记录</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 自适应阈值 */}
          {statistics?.adaptiveThresholds && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4">自适应配置</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="font-medium">检测间隔</div>
                    <div className="text-gray-600">
                      {Math.round(statistics.adaptiveThresholds.detectionInterval / (60 * 60 * 1000))} 小时
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">缓存过期</div>
                    <div className="text-gray-600">
                      {Math.round(statistics.adaptiveThresholds.cacheExpiry / (60 * 1000))} 分钟
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">错误容错</div>
                    <div className="text-gray-600">
                      {statistics.adaptiveThresholds.errorTolerance ? '启用' : '禁用'}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">跳过已知浏览器</div>
                    <div className="text-gray-600">
                      {statistics.adaptiveThresholds.skipDetectionForKnownBrowsers ? '启用' : '禁用'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 刷新按钮 */}
          <div className="text-center">
            <button
              onClick={loadData}
              disabled={loading}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50"
            >
              刷新数据
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrowserCompatibilityDashboard;
