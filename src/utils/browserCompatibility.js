/**
 * 浏览器兼容性检测工具
 */

// 延迟导入以避免构建时的循环依赖问题
let smartDetectionStrategy = null;
let browserPerformanceMonitor = null;

// 异步加载依赖
const loadDependencies = async () => {
  if (!smartDetectionStrategy) {
    try {
      const { default: strategy } = await import('./smartDetectionStrategy.js');
      smartDetectionStrategy = strategy;
    } catch (e) {
      console.warn('Smart detection strategy not available:', e);
    }
  }

  if (!browserPerformanceMonitor) {
    try {
      const { default: monitor } = await import('./browserPerformanceMonitor.js');
      browserPerformanceMonitor = monitor;
    } catch (e) {
      console.warn('Performance monitor not available:', e);
    }
  }
};

// 浏览器最低版本要求 - 基于项目实际使用的特性
export const MINIMUM_BROWSER_VERSIONS = {
  Chrome: 60,    // 支持 ES2020, async/await, modern CSS Grid
  Firefox: 60,   // 支持 ES2020, 现代 JS 特性
  Safari: 12,    // 支持 CSS Grid, ES2020, Service Worker
  Edge: 79,      // 基于 Chromium 的现代 Edge
  Opera: 57,     // 基于 Chromium
  Samsung: 10,   // 移动端 Samsung Internet
  'Internet Explorer': 999, // IE 完全不支持现代特性
  '360SE': 13,   // 360安全浏览器
  'WeChat': 7.0, // 微信内置浏览器
  'UC': 13,      // UC浏览器
  'QQ': 10,      // QQ浏览器
  'Baidu': 13,   // 百度浏览器
  'Sogou': 11    // 搜狗浏览器
};

// 推荐浏览器版本要求 - 完整功能支持
export const RECOMMENDED_BROWSER_VERSIONS = {
  Chrome: 80,    // 现代Chrome，完整的现代Web API支持
  Firefox: 80,   // 现代Firefox，完整的现代Web API支持
  Safari: 14,    // 现代Safari，完整的现代Web API支持
  Edge: 80,      // 现代Edge（基于Chromium）
  Opera: 70,     // 现代Opera（基于Chromium）
  Samsung: 12,   // 移动端 Samsung Internet
  'Internet Explorer': 999, // IE 完全不支持
  '360SE': 16,   // 360安全浏览器推荐版本
  'WeChat': 8.0, // 微信内置浏览器
  'UC': 15,      // UC浏览器
  'QQ': 12,      // QQ浏览器
  'Baidu': 15,   // 百度浏览器
  'Sogou': 13    // 搜狗浏览器
};

// 集成服务特定要求
export const INTEGRATION_REQUIREMENTS = {
  OpenVSCode: {
    Chrome: 66,    // Clipboard API 支持
    Firefox: 63,   // Clipboard API 支持
    Safari: 13.1,  // Clipboard API 支持
    Edge: 79       // Clipboard API 支持
  },
  LobeChat: {
    Chrome: 53,    // getUserMedia 支持
    Firefox: 36,   // getUserMedia 支持
    Safari: 11,    // getUserMedia 支持
    Edge: 12       // getUserMedia 支持
  }
};

/**
 * 检测浏览器信息
 * @returns {Object} 浏览器信息对象
 */
export const getBrowserInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const browser = {
    name: 'Unknown',
    version: 0,
    fullVersion: 'Unknown',
    engine: 'Unknown',
    platform: 'Unknown'
  };

  // 检测浏览器平台
  if (userAgent.indexOf('windows') > -1) browser.platform = 'Windows';
  else if (userAgent.indexOf('macintosh') > -1) browser.platform = 'macOS';
  else if (userAgent.indexOf('linux') > -1) browser.platform = 'Linux';
  else if (userAgent.indexOf('android') > -1) browser.platform = 'Android';
  else if (userAgent.indexOf('iphone') > -1 || userAgent.indexOf('ipad') > -1) browser.platform = 'iOS';

  // 检测浏览器类型和版本
  let match;

  // 微信内置浏览器检测 (优先检测)
  if (userAgent.indexOf('micromessenger') > -1) {
    browser.name = 'WeChat';
    if ((match = userAgent.match(/micromessenger\/(\d+)\.(\d+)/))) {
      browser.version = parseFloat(`${match[1]}.${match[2]}`);
      browser.fullVersion = `${match[1]}.${match[2]}`;
    } else {
      browser.version = 7.0; // 默认版本
      browser.fullVersion = '7.0.0';
    }
    browser.engine = 'WebKit';
    browser.isMobile = true;
  }
  // UC浏览器检测
  else if (userAgent.indexOf('ucbrowser') > -1 || userAgent.indexOf('uc browser') > -1) {
    browser.name = 'UC';
    if ((match = userAgent.match(/ucbrowser\/(\d+)\.(\d+)/))) {
      browser.version = parseInt(match[1]);
      browser.fullVersion = `${match[1]}.${match[2]}`;
    } else {
      browser.version = 13; // 默认版本
      browser.fullVersion = '13.0';
    }
    browser.engine = 'WebKit';
    browser.isMobile = true;
  }
  // QQ浏览器检测
  else if (userAgent.indexOf('qqbrowser') > -1 || userAgent.indexOf('mqqbrowser') > -1) {
    browser.name = 'QQ';
    if ((match = userAgent.match(/(?:qqbrowser|mqqbrowser)\/(\d+)\.(\d+)/))) {
      browser.version = parseInt(match[1]);
      browser.fullVersion = `${match[1]}.${match[2]}`;
    } else {
      browser.version = 10; // 默认版本
      browser.fullVersion = '10.0';
    }
    browser.engine = 'WebKit';
    browser.isMobile = userAgent.indexOf('mqqbrowser') > -1;
  }
  // 百度浏览器检测
  else if (userAgent.indexOf('baidubrowser') > -1 || userAgent.indexOf('bidubrowser') > -1) {
    browser.name = 'Baidu';
    if ((match = userAgent.match(/(?:baidubrowser|bidubrowser)\/(\d+)\.(\d+)/))) {
      browser.version = parseInt(match[1]);
      browser.fullVersion = `${match[1]}.${match[2]}`;
    } else {
      browser.version = 13; // 默认版本
      browser.fullVersion = '13.0';
    }
    browser.engine = 'WebKit';
  }
  // 搜狗浏览器检测
  else if (userAgent.indexOf('se ') > -1 && userAgent.indexOf('metasr') > -1) {
    browser.name = 'Sogou';
    if ((match = userAgent.match(/se (\d+)\.(\d+)/))) {
      browser.version = parseInt(match[1]);
      browser.fullVersion = `${match[1]}.${match[2]}`;
    } else {
      browser.version = 11; // 默认版本
      browser.fullVersion = '11.0';
    }
    browser.engine = 'WebKit';
  }
  // Chrome (必须在 Edge 之前检测)
  else if ((match = userAgent.match(/chrome\/(\d+)\.(\d+)\.(\d+)/)) && userAgent.indexOf('edge') === -1 && userAgent.indexOf('opr') === -1) {
    browser.name = 'Chrome';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}.${match[3]}`;
    browser.engine = 'Blink';
  }
  // Firefox
  else if ((match = userAgent.match(/firefox\/(\d+)\.(\d+)/))) {
    browser.name = 'Firefox';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'Gecko';
  }
  // Safari (必须在 Chrome 之前检测，因为 Chrome 也包含 safari 字符串)
  else if ((match = userAgent.match(/version\/(\d+)\.(\d+).*safari/)) && userAgent.indexOf('chrome') === -1) {
    browser.name = 'Safari';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'WebKit';
  }
  // Edge (新版基于 Chromium)
  else if ((match = userAgent.match(/edg\/(\d+)\.(\d+)\.(\d+)/))) {
    browser.name = 'Edge';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}.${match[3]}`;
    browser.engine = 'Blink';
  }
  // Edge (旧版)
  else if ((match = userAgent.match(/edge\/(\d+)\.(\d+)/))) {
    browser.name = 'Edge';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'EdgeHTML';
  }
  // Opera
  else if ((match = userAgent.match(/opr\/(\d+)\.(\d+)/))) {
    browser.name = 'Opera';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'Blink';
  }
  // Samsung Internet
  else if ((match = userAgent.match(/samsungbrowser\/(\d+)\.(\d+)/))) {
    browser.name = 'Samsung';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'Blink';
  }
  // Internet Explorer
  else if ((match = userAgent.match(/(?:msie |trident.*rv:)(\d+)\.?(\d+)?/))) {
    browser.name = 'Internet Explorer';
    browser.version = parseInt(match[1]);
    browser.fullVersion = match[2] ? `${match[1]}.${match[2]}` : match[1];
    browser.engine = 'Trident';
  }
  // 360安全浏览器
  else if ((match = userAgent.match(/360se/))) {
    browser.name = '360SE';
    // 360浏览器版本检测比较复杂，尝试从不同位置获取版本号
    const versionMatch = userAgent.match(/360se\/(\d+)\.(\d+)/) ||
                        userAgent.match(/360 (\d+)\.(\d+)/) ||
                        userAgent.match(/qihu 360se (\d+)\.(\d+)/);
    if (versionMatch) {
      browser.version = parseInt(versionMatch[1]);
      browser.fullVersion = `${versionMatch[1]}.${versionMatch[2]}`;
    } else {
      // 如果无法检测到具体版本，假设是较新版本
      browser.version = 13;
      browser.fullVersion = '13.0';
    }
    browser.engine = 'Blink'; // 现代360浏览器基于Chromium
  }
  // QQ浏览器
  else if ((match = userAgent.match(/qqbrowser\/(\d+)\.(\d+)/))) {
    browser.name = 'QQBrowser';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'Blink';
  }

  return browser;
};

/**
 * 检测浏览器特性支持
 * @returns {Object} 特性支持检测结果
 */
export const checkFeatureSupport = () => {
  const features = {};

  // JavaScript 特性检测
  features.es6Support = (() => {
    try {
      // 检测箭头函数、模板字符串、解构赋值等特性
      // 使用更安全的方式检测而不是 eval
      return (
        typeof Symbol !== 'undefined' &&
        typeof Promise !== 'undefined' &&
        typeof Map !== 'undefined' &&
        typeof Set !== 'undefined' &&
        Array.prototype.includes &&
        Object.assign &&
        // 检测箭头函数支持
        (() => {
          try {
            return (function() { return () => {}; })()() === undefined;
          } catch(e) {
            return false;
          }
        })()
      );
    } catch (e) {
      return false;
    }
  })();

  features.es2017Support = (() => {
    try {
      // 检测 async/await 支持
      return typeof (async () => {})().then === 'function';
    } catch (e) {
      return false;
    }
  })();

  // API 特性检测
  features.fetchAPI = typeof fetch !== 'undefined';
  features.promiseSupport = typeof Promise !== 'undefined' && typeof Promise.prototype.finally === 'function';
  features.arrayMethods = Array.prototype.includes && Array.prototype.find && Array.prototype.findIndex;
  features.objectMethods = Object.assign && Object.keys && Object.values;
  features.mapSet = typeof Map !== 'undefined' && typeof Set !== 'undefined';
  features.symbols = typeof Symbol !== 'undefined';
  features.weakMapSet = typeof WeakMap !== 'undefined' && typeof WeakSet !== 'undefined';

  // Web API 特性检测
  features.webSocket = typeof WebSocket !== 'undefined';
  features.webWorker = typeof Worker !== 'undefined';
  features.serviceWorker = 'serviceWorker' in navigator;
  features.pushNotifications = 'PushManager' in window;
  features.geolocation = 'geolocation' in navigator;
  features.deviceOrientation = 'DeviceOrientationEvent' in window;
  features.vibration = 'vibrate' in navigator;

  // Storage 特性检测
  features.localStorage = (() => {
    try {
      return typeof localStorage !== 'undefined' && localStorage !== null;
    } catch (e) {
      return false;
    }
  })();

  features.sessionStorage = (() => {
    try {
      return typeof sessionStorage !== 'undefined' && sessionStorage !== null;
    } catch (e) {
      return false;
    }
  })();

  features.indexedDB = 'indexedDB' in window;

  // 移动端特性检测
  features.touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  features.orientationSupport = 'orientation' in window;
  features.deviceMotion = 'DeviceMotionEvent' in window;

  // 网络状态检测
  features.onlineStatus = 'onLine' in navigator;
  features.connectionAPI = 'connection' in navigator || 'mozConnection' in navigator || 'webkitConnection' in navigator;

  // 性能API检测
  features.performanceAPI = 'performance' in window && 'now' in performance;
  features.performanceObserver = 'PerformanceObserver' in window;
  features.intersectionObserver = 'IntersectionObserver' in window;

  // 现代JavaScript特性检测
  features.modules = (() => {
    try {
      return typeof Symbol !== 'undefined' && 'iterator' in Symbol;
    } catch (e) {
      return false;
    }
  })();

  features.dynamicImport = (() => {
    try {
      // 检测动态导入支持，使用更安全的方式
      return typeof Function('return import("data:text/javascript,export default {}")') === 'function';
    } catch (e) {
      return false;
    }
  })();

  // CSS 特性检测
  const testElement = document.createElement('div');
  features.cssGrid = 'grid' in testElement.style;
  features.cssFlexbox = 'flex' in testElement.style || 'webkitFlex' in testElement.style;
  features.cssCustomProperties = CSS && CSS.supports && CSS.supports('--custom-property', 'value');
  features.cssTransforms = 'transform' in testElement.style || 'webkitTransform' in testElement.style;
  features.cssTransitions = 'transition' in testElement.style || 'webkitTransition' in testElement.style;
  features.cssAnimations = 'animation' in testElement.style || 'webkitAnimation' in testElement.style;

  // 媒体特性检测
  features.webGL = (() => {
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch (e) {
      return false;
    }
  })();

  features.canvas = (() => {
    try {
      return !!document.createElement('canvas').getContext('2d');
    } catch (e) {
      return false;
    }
  })();

  features.svg = document.implementation.hasFeature('http://www.w3.org/TR/SVG11/feature#BasicStructure', '1.1');
  features.audio = !!document.createElement('audio').canPlayType;
  features.video = !!document.createElement('video').canPlayType;

  // 输入特性检测
  features.touch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  features.pointerEvents = 'onpointerdown' in window;

  // 集成服务特性检测
  features.clipboardAPI = (() => {
    try {
      return 'clipboard' in navigator &&
             'writeText' in navigator.clipboard &&
             'readText' in navigator.clipboard;
    } catch (e) {
      return false;
    }
  })();

  features.fileSystemAccess = (() => {
    try {
      return 'showOpenFilePicker' in window &&
             'showSaveFilePicker' in window &&
             'showDirectoryPicker' in window;
    } catch (e) {
      return false;
    }
  })();

  features.getUserMedia = (() => {
    try {
      return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    } catch (e) {
      return false;
    }
  })();

  features.webRTC = (() => {
    try {
      return !!(window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection);
    } catch (e) {
      return false;
    }
  })();

  // ES2022 特性检测 - 使用安全的检测方法
  features.es2022Support = (() => {
    try {
      // 检测 top-level await 支持 - 安全检测
      const hasTopLevelAwait = (() => {
        try {
          // 检查是否支持动态 import 和 async/await
          // 避免直接使用 typeof import，因为它在构建时会出错
          const hasPromiseFinally = typeof Promise !== 'undefined' &&
                                   typeof Promise.prototype.finally === 'function';
          const hasAsyncAwait = typeof (async () => {})().then === 'function';

          // 检测动态 import 支持（间接方式）
          const hasDynamicImport = typeof window !== 'undefined' &&
                                  'import' in window.constructor.prototype;

          return hasPromiseFinally && hasAsyncAwait;
        } catch (e) {
          return false;
        }
      })();

      // 检测私有类字段支持 - 安全检测
      const hasPrivateFields = (() => {
        try {
          // 更安全的语法检测，避免在旧浏览器中抛出语法错误
          if (typeof Function === 'undefined') return false;

          // 使用 eval 的替代方案，更安全
          const testCode = 'class Test { constructor() { this.test = 1; } }';
          new Function(testCode);

          // 如果基础类语法都不支持，肯定不支持私有字段
          return typeof Symbol !== 'undefined';
        } catch (e) {
          return false;
        }
      })();

      // 检测逻辑赋值操作符 - 安全检测
      const hasLogicalAssignment = (() => {
        try {
          if (typeof Function === 'undefined') return false;

          // 使用更安全的检测方法
          const testCode = 'var a = {}; a.b = a.b || 1;';
          new Function(testCode);
          return true;
        } catch (e) {
          return false;
        }
      })();

      return hasTopLevelAwait && hasPrivateFields && hasLogicalAssignment;
    } catch (e) {
      // 在任何错误情况下都返回 false，确保不会破坏旧浏览器
      return false;
    }
  })();

  // iframe 安全特性检测
  features.iframeSandbox = (() => {
    try {
      const iframe = document.createElement('iframe');
      return 'sandbox' in iframe;
    } catch (e) {
      return false;
    }
  })();

  return features;
};

/**
 * 综合兼容性检测（原始版本）
 * @returns {Object} 兼容性检测结果
 */
const performBrowserCompatibilityCheck = () => {
  try {
    // 标记检测开始
    if (performance.mark) {
      performance.mark('compatibility-check-start');
    }

    const browser = getBrowserInfo();
    const features = checkFeatureSupport();

  // 检查浏览器版本是否满足最低要求
  const isVersionSupported = MINIMUM_BROWSER_VERSIONS[browser.name]
    ? browser.version >= MINIMUM_BROWSER_VERSIONS[browser.name]
    : browser.name !== 'Internet Explorer'; // IE 完全不支持

  // 检查是否满足推荐版本要求
  let isRecommendedVersion;
  if (RECOMMENDED_BROWSER_VERSIONS[browser.name]) {
    isRecommendedVersion = browser.version >= RECOMMENDED_BROWSER_VERSIONS[browser.name];

    // 对于现代浏览器，即使低于我们定义的推荐版本，也应该被认为是现代的
    // 这是为了避免因为我们的推荐版本定义过高而误报
    if (!isRecommendedVersion && isVersionSupported) {
      const modernVersionThresholds = {
        'Chrome': 100,   // Chrome 100+ 都是现代版本
        'Firefox': 100,  // Firefox 100+ 都是现代版本
        'Safari': 15,    // Safari 15+ 都是现代版本
        'Edge': 100      // Edge 100+ 都是现代版本
      };

      const modernThreshold = modernVersionThresholds[browser.name];
      if (modernThreshold && browser.version >= modernThreshold) {
        isRecommendedVersion = true;
        console.log(`[BrowserCompatibility] ${browser.name} ${browser.version} 被认定为现代浏览器`);
      }
    }
  } else {
    // 如果没有定义推荐版本，但基本兼容，则认为达到推荐
    isRecommendedVersion = isVersionSupported;
  }

  // 检查关键特性是否支持
  const criticalFeatures = [
    'es6Support',
    'fetchAPI',
    'promiseSupport',
    'localStorage',
    'cssFlexbox',
    'webSocket',
    'iframeSandbox'
  ];

  const unsupportedFeatures = criticalFeatures.filter(feature => !features[feature]);
  const isFeaturesSupported = unsupportedFeatures.length === 0;

  // 检查推荐特性是否支持
  const recommendedFeatures = [
    'es2017Support',
    'es2022Support',
    'serviceWorker',
    'cssGrid',
    'clipboardAPI',
    'getUserMedia'
  ];

  const missingRecommendedFeatures = recommendedFeatures.filter(feature => !features[feature]);

  // 检查集成服务兼容性
  const integrationCompatibility = checkIntegrationCompatibility(browser, features);

  const isCompatible = isVersionSupported && isFeaturesSupported;

  return {
    isCompatible,
    isRecommendedVersion,
    browser,
    features,
    integrationCompatibility,
    issues: {
      versionTooOld: !isVersionSupported,
      belowRecommended: !isRecommendedVersion,
      unsupportedFeatures,
      missingRecommendedFeatures,
      integrationIssues: integrationCompatibility.issues
    },
    recommendations: getCompatibilityRecommendations(browser, isCompatible, unsupportedFeatures, integrationCompatibility)
  };
  } catch (error) {
    // 在任何错误情况下返回一个安全的默认值
    console.warn('浏览器兼容性检测失败，使用默认值:', error);

    // 返回一个默认的兼容性结果，假设浏览器兼容
    return {
      isCompatible: true,
      isRecommendedVersion: true,
      browser: {
        name: 'Unknown',
        version: 0,
        fullVersion: 'Unknown',
        engine: 'Unknown',
        platform: 'Unknown'
      },
      features: {},
      integrationCompatibility: {
        OpenVSCode: { compatible: true, issues: [], features: {} },
        LobeChat: { compatible: true, issues: [], features: {} },
        issues: [],
        hasIssues: false
      },
      issues: {
        versionTooOld: false,
        belowRecommended: false,
        unsupportedFeatures: [],
        missingRecommendedFeatures: [],
        integrationIssues: []
      },
      recommendations: []
    };
  }
};

/**
 * 智能浏览器兼容性检测（优化版本）
 * 使用智能策略减少不必要的检测，提升性能
 * @returns {Promise<Object>} 兼容性检测结果
 */
export const checkBrowserCompatibility = async () => {
  try {
    await loadDependencies();

    if (smartDetectionStrategy && smartDetectionStrategy.optimizeDetection) {
      const optimizedCheck = smartDetectionStrategy.optimizeDetection(
        () => performBrowserCompatibilityCheck()
      );
      return await optimizedCheck(navigator.userAgent);
    } else {
      // 降级到基础检测
      return performBrowserCompatibilityCheck();
    }
  } catch (error) {
    console.warn('[BrowserCompatibility] Smart detection failed, using basic check:', error);
    return performBrowserCompatibilityCheck();
  }
};

/**
 * 强制执行完整的浏览器兼容性检测
 * 绕过智能缓存，总是执行完整检测
 * @returns {Object} 兼容性检测结果
 */
export const forceCheckBrowserCompatibility = () => {
  console.log('[BrowserCompatibility] Force checking compatibility');
  return performBrowserCompatibilityCheck();
};

/**
 * 获取检测统计信息
 * @returns {Object} 统计信息
 */
export const getDetectionStatistics = async () => {
  try {
    await loadDependencies();
    return smartDetectionStrategy ? smartDetectionStrategy.getStatistics() : {};
  } catch (error) {
    console.warn('[BrowserCompatibility] Failed to get statistics:', error);
    return {};
  }
};

/**
 * 清除检测缓存
 */
export const clearDetectionCache = async () => {
  try {
    await loadDependencies();
    if (smartDetectionStrategy) {
      smartDetectionStrategy.clearCache();
    }
  } catch (error) {
    console.warn('[BrowserCompatibility] Failed to clear cache:', error);
  }
};

/**
 * 启用性能监控
 */
export const enablePerformanceMonitoring = async () => {
  try {
    await loadDependencies();
    if (browserPerformanceMonitor && browserPerformanceMonitor.constructor.enable) {
      browserPerformanceMonitor.constructor.enable();
    } else {
      // 降级方案
      localStorage.setItem('browser-monitoring-enabled', 'true');
    }
  } catch (error) {
    console.warn('[BrowserCompatibility] Failed to enable monitoring:', error);
  }
};

/**
 * 禁用性能监控
 */
export const disablePerformanceMonitoring = async () => {
  try {
    await loadDependencies();
    if (browserPerformanceMonitor && browserPerformanceMonitor.constructor.disable) {
      browserPerformanceMonitor.constructor.disable();
    } else {
      // 降级方案
      localStorage.removeItem('browser-monitoring-enabled');
    }
  } catch (error) {
    console.warn('[BrowserCompatibility] Failed to disable monitoring:', error);
  }
};

/**
 * 检查集成服务兼容性
 * @param {Object} browser 浏览器信息
 * @param {Object} features 特性支持信息
 * @returns {Object} 集成兼容性结果
 */
export const checkIntegrationCompatibility = (browser, features) => {
  try {
    // 安全检查输入参数
    if (!browser || typeof browser !== 'object') {
      browser = { name: 'Unknown', version: 0 };
    }
    if (!features || typeof features !== 'object') {
      features = {};
    }

    const results = {
      OpenVSCode: {
        compatible: true,
        issues: [],
        features: {}
      },
      LobeChat: {
        compatible: true,
        issues: [],
        features: {}
      }
    };

  // OpenVSCode 兼容性检查
  const openVSCodeRequirements = INTEGRATION_REQUIREMENTS.OpenVSCode;
  if (openVSCodeRequirements[browser.name]) {
    const requiredVersion = openVSCodeRequirements[browser.name];
    if (browser.version < requiredVersion) {
      results.OpenVSCode.compatible = false;
      results.OpenVSCode.issues.push(`需要 ${browser.name} ${requiredVersion}+ 以获得完整的 OpenVSCode 功能`);
    }
  }

  // OpenVSCode 特性检查
  results.OpenVSCode.features = {
    clipboardAPI: features.clipboardAPI,
    fileSystemAccess: features.fileSystemAccess,
    webSocket: features.webSocket,
    iframeSandbox: features.iframeSandbox
  };

  if (!features.clipboardAPI) {
    results.OpenVSCode.issues.push('剪贴板 API 不支持，复制粘贴功能可能受限');
  }
  if (!features.fileSystemAccess) {
    results.OpenVSCode.issues.push('文件系统访问 API 不支持，文件操作将使用传统方式');
  }

  // LobeChat 兼容性检查
  const lobeChatRequirements = INTEGRATION_REQUIREMENTS.LobeChat;
  if (lobeChatRequirements[browser.name]) {
    const requiredVersion = lobeChatRequirements[browser.name];
    if (browser.version < requiredVersion) {
      results.LobeChat.compatible = false;
      results.LobeChat.issues.push(`需要 ${browser.name} ${requiredVersion}+ 以获得完整的 LobeChat 功能`);
    }
  }

  // LobeChat 特性检查
  results.LobeChat.features = {
    getUserMedia: features.getUserMedia,
    webRTC: features.webRTC,
    clipboardAPI: features.clipboardAPI,
    iframeSandbox: features.iframeSandbox
  };

  if (!features.getUserMedia) {
    results.LobeChat.issues.push('媒体设备访问不支持，语音/视频功能不可用');
  }
  if (!features.webRTC) {
    results.LobeChat.issues.push('WebRTC 不支持，实时通信功能可能受限');
  }

  // 汇总问题
  const allIssues = [
    ...results.OpenVSCode.issues,
    ...results.LobeChat.issues
  ];

  return {
    ...results,
    issues: allIssues,
    hasIssues: allIssues.length > 0
  };
  } catch (error) {
    // 在错误情况下返回安全的默认值
    console.warn('集成兼容性检测失败，使用默认值:', error);
    return {
      OpenVSCode: { compatible: true, issues: [], features: {} },
      LobeChat: { compatible: true, issues: [], features: {} },
      issues: [],
      hasIssues: false
    };
  }
};

/**
 * 获取兼容性建议
 * @param {Object} browser 浏览器信息
 * @param {boolean} isCompatible 是否兼容
 * @param {Array} unsupportedFeatures 不支持的特性列表
 * @param {Object} integrationCompatibility 集成兼容性信息
 * @returns {Array} 建议列表
 */
export const getCompatibilityRecommendations = (browser, isCompatible, unsupportedFeatures = [], integrationCompatibility = null) => {
  const recommendations = [];

  if (!isCompatible) {
    if (browser.name === 'Internet Explorer') {
      recommendations.push({
        type: 'critical',
        message: 'Internet Explorer 已不再受支持，请升级到现代浏览器以获得最佳体验',
        action: 'upgrade'
      });
    } else if (MINIMUM_BROWSER_VERSIONS[browser.name] && browser.version < MINIMUM_BROWSER_VERSIONS[browser.name]) {
      recommendations.push({
        type: 'warning',
        message: `您的 ${browser.name} 版本 (${browser.version}) 过旧，建议升级到 ${MINIMUM_BROWSER_VERSIONS[browser.name]} 或更高版本`,
        action: 'update'
      });
    }

    if (unsupportedFeatures.includes('es6Support')) {
      recommendations.push({
        type: 'critical',
        message: '浏览器不支持现代 JavaScript 语法，网站功能可能无法正常工作',
        action: 'upgrade'
      });
    }

    if (unsupportedFeatures.includes('fetchAPI')) {
      recommendations.push({
        type: 'warning',
        message: '浏览器不支持 Fetch API，网络请求可能受到影响',
        action: 'polyfill'
      });
    }

    if (unsupportedFeatures.includes('localStorage')) {
      recommendations.push({
        type: 'warning',
        message: '浏览器不支持本地存储，某些功能可能无法保存设置',
        action: 'fallback'
      });
    }
  }

  // 添加集成服务相关建议
  if (integrationCompatibility && integrationCompatibility.hasIssues) {
    // OpenVSCode 相关建议
    if (!integrationCompatibility.OpenVSCode.compatible) {
      recommendations.push({
        type: 'warning',
        message: 'OpenVSCode 功能可能受限，建议升级浏览器以获得完整的代码编辑体验',
        action: 'update',
        service: 'OpenVSCode'
      });
    }

    if (integrationCompatibility.OpenVSCode.issues.length > 0) {
      integrationCompatibility.OpenVSCode.issues.forEach(issue => {
        recommendations.push({
          type: 'info',
          message: `OpenVSCode: ${issue}`,
          action: 'info',
          service: 'OpenVSCode'
        });
      });
    }

    // LobeChat 相关建议
    if (!integrationCompatibility.LobeChat.compatible) {
      recommendations.push({
        type: 'warning',
        message: 'LobeChat 功能可能受限，建议升级浏览器以获得完整的智能对话体验',
        action: 'update',
        service: 'LobeChat'
      });
    }

    if (integrationCompatibility.LobeChat.issues.length > 0) {
      integrationCompatibility.LobeChat.issues.forEach(issue => {
        recommendations.push({
          type: 'info',
          message: `LobeChat: ${issue}`,
          action: 'info',
          service: 'LobeChat'
        });
      });
    }
  }

  return recommendations;
};



/**
 * 获取推荐浏览器列表
 * @returns {Array} 推荐浏览器列表
 */
export const getRecommendedBrowsers = () => [
  {
    name: 'Google Chrome',
    minVersion: MINIMUM_BROWSER_VERSIONS.Chrome,
    downloadUrl: 'https://www.google.com/chrome/',
    localDownloadUrl: null, // Chrome安装包需要从官网下载
    icon: '🌐',
    description: '快速、安全、功能丰富',
    updateInstructions: [
      '点击浏览器右上角的三个点菜单',
      '选择"帮助" > "关于 Google Chrome"',
      '浏览器会自动检查并下载更新'
    ]
  },
  {
    name: 'Mozilla Firefox',
    minVersion: MINIMUM_BROWSER_VERSIONS.Firefox,
    downloadUrl: 'https://www.mozilla.org/firefox/',
    localDownloadUrl: null, // Firefox安装包需要从官网下载
    icon: '🦊',
    description: '注重隐私、开源、可定制',
    updateInstructions: [
      '点击浏览器右上角的菜单按钮',
      '选择"帮助" > "关于 Firefox"',
      '浏览器会自动检查并下载更新'
    ]
  },
  {
    name: 'Microsoft Edge',
    minVersion: MINIMUM_BROWSER_VERSIONS.Edge,
    downloadUrl: 'https://www.microsoft.com/edge/',
    localDownloadUrl: null, // Edge通常随Windows更新
    icon: '🔷',
    description: 'Windows 系统推荐浏览器',
    updateInstructions: [
      '点击浏览器右上角的三个点菜单',
      '选择"帮助和反馈" > "关于 Microsoft Edge"',
      '浏览器会自动检查并下载更新'
    ]
  },
  {
    name: '360安全浏览器',
    minVersion: MINIMUM_BROWSER_VERSIONS['360SE'],
    downloadUrl: 'https://browser.360.cn/',
    localDownloadUrl: '/downloads/browser/360se16.1.2128.64.exe',
    icon: '🛡️',
    description: '国产浏览器，安全防护强',
    updateInstructions: [
      '点击浏览器右上角的菜单按钮',
      '选择"帮助" > "检查更新"',
      '按照提示下载并安装更新'
    ]
  }
];

/**
 * 记录兼容性检测结果（用于统计分析）
 * @param {Object} compatibilityResult 兼容性检测结果
 */
export const logCompatibilityData = (compatibilityResult) => {
  try {
    const logData = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      browser: compatibilityResult.browser,
      isCompatible: compatibilityResult.isCompatible,
      issues: compatibilityResult.issues,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      }
    };

    // 可以发送到分析服务器或保存到本地存储
    console.log('Browser Compatibility Check:', logData);
    
    // 如果支持，保存到本地存储用于调试
    if (compatibilityResult.features.localStorage) {
      localStorage.setItem('browserCompatibilityLog', JSON.stringify(logData));
    }
  } catch (error) {
    console.warn('Failed to log compatibility data:', error);
  }
};