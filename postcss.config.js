export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    // 生产环境启用CSS压缩和优化
    ...(process.env.NODE_ENV === 'production' ? {
      'postcss-import': {},
      'postcss-nested': {},
      cssnano: {
        preset: ['default', {
          discardComments: { removeAll: true },
          normalizeWhitespace: true,
          mergeLonghand: true,
          mergeRules: true,
          minifySelectors: true,
          reduceIdents: false, // 保持动画名称
          zindex: false // 不优化z-index
        }]
      }
    } : {})
  },
}