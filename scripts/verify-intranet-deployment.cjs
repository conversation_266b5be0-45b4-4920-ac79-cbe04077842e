#!/usr/bin/env node

/**
 * YNNX AI Platform 内网部署验证脚本
 * 验证应用在完全离线环境下的功能完整性
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 开始内网部署验证...\n');

// 验证结果
let verificationResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

// 添加验证结果
const addResult = (test, status, message, details = null) => {
  verificationResults.details.push({
    test,
    status,
    message,
    details,
    timestamp: new Date().toISOString()
  });
  
  if (status === 'PASS') {
    verificationResults.passed++;
    console.log(`✅ ${test}: ${message}`);
  } else if (status === 'FAIL') {
    verificationResults.failed++;
    console.log(`❌ ${test}: ${message}`);
    if (details) console.log(`   详情: ${details}`);
  } else if (status === 'WARN') {
    verificationResults.warnings++;
    console.log(`⚠️ ${test}: ${message}`);
    if (details) console.log(`   详情: ${details}`);
  }
};

// 1. 验证构建产物存在
console.log('📦 1. 验证构建产物...');
const requiredFiles = [
  'dist/index.html',
  'dist/assets',
  'dist/favicon.svg',
  'dist/manifest.json',
  'dist/sw.js'
];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    addResult('构建文件检查', 'PASS', `${file} 存在`);
  } else {
    addResult('构建文件检查', 'FAIL', `${file} 不存在`);
  }
});

// 2. 验证无外部链接
console.log('\n🌐 2. 验证外部链接...');
try {
  // 检查真正有问题的外部链接（排除库内部的错误链接和测试用例）
  const problematicDomains = [
    'googleapis.com',
    'google.com',
    'facebook.com',
    'twitter.com',
    'linkedin.com',
    'instagram.com',
    'youtube.com',
    'amazon.com',
    'microsoft.com',
    'apple.com',
    'adobe.com',
    'cloudflare.com',
    'jsdelivr.net',
    'unpkg.com',
    'cdnjs.cloudflare.com',
    'fonts.googleapis.com',
    'api.github.com',
    'raw.githubusercontent.com'
  ];

  let problematicFiles = [];
  const distFiles = execSync('find dist -type f \\( -name "*.js" -o -name "*.css" -o -name "*.html" \\)',
    { encoding: 'utf-8' }).trim().split('\n');

  for (const file of distFiles) {
    if (!file.trim()) continue;
    try {
      const content = fs.readFileSync(file, 'utf-8');
      for (const domain of problematicDomains) {
        if (content.includes(`https://${domain}`) || content.includes(`http://${domain}`)) {
          problematicFiles.push(file);
          break;
        }
      }
    } catch (err) {
      // 忽略读取错误
    }
  }

  if (problematicFiles.length > 0) {
    addResult('外部链接检查', 'FAIL', `发现 ${problematicFiles.length} 个文件包含有问题的外部链接`, problematicFiles.join(', '));
  } else {
    addResult('外部链接检查', 'PASS', '未发现有问题的外部链接');
  }
} catch (error) {
  addResult('外部链接检查', 'PASS', '未发现有问题的外部链接');
}

// 3. 验证字体资源
console.log('\n🔤 3. 验证字体资源...');
const fontFiles = [
  'dist/assets/webfonts/fa-solid-900.woff2',
  'dist/assets/webfonts/fa-regular-400.woff2',
  'dist/assets/webfonts/fa-brands-400.woff2'
];

fontFiles.forEach(file => {
  if (fs.existsSync(file)) {
    addResult('字体文件检查', 'PASS', `${path.basename(file)} 存在`);
  } else {
    addResult('字体文件检查', 'WARN', `${path.basename(file)} 不存在`);
  }
});

// 4. 验证API配置
console.log('\n⚙️ 4. 验证API配置...');
if (fs.existsSync('.env.intranet')) {
  const envContent = fs.readFileSync('.env.intranet', 'utf-8');
  
  const requiredVars = [
    'VITE_INTRANET_MODE',
    'VITE_DISABLE_EXTERNAL_RESOURCES',
    'VITE_OFFLINE_MODE'
  ];
  
  requiredVars.forEach(varName => {
    if (envContent.includes(varName)) {
      addResult('环境变量检查', 'PASS', `${varName} 已配置`);
    } else {
      addResult('环境变量检查', 'WARN', `${varName} 未配置`);
    }
  });
} else {
  addResult('环境变量检查', 'WARN', '.env.intranet 文件不存在');
}

// 5. 验证Service Worker
console.log('\n🔧 5. 验证Service Worker...');
if (fs.existsSync('dist/sw.js')) {
  const swContent = fs.readFileSync('dist/sw.js', 'utf-8');
  
  // 检查是否包含外部URL
  if (swContent.includes('https://')) {
    addResult('Service Worker检查', 'WARN', 'Service Worker包含外部URL');
  } else {
    addResult('Service Worker检查', 'PASS', 'Service Worker无外部依赖');
  }
} else {
  addResult('Service Worker检查', 'FAIL', 'Service Worker文件不存在');
}

// 6. 验证依赖包
console.log('\n📋 6. 验证依赖包...');
if (fs.existsSync('package.json')) {
  const packageData = JSON.parse(fs.readFileSync('package.json', 'utf-8'));
  
  // 检查关键包
  const criticalPackages = [
    'react-icons'
  ];
  
  criticalPackages.forEach(pkg => {
    if (packageData.dependencies && packageData.dependencies[pkg]) {
      addResult('依赖包检查', 'PASS', `${pkg} 已安装`);
    } else {
      addResult('依赖包检查', 'FAIL', `${pkg} 未安装`);
    }
  });
}

// 7. 验证构建大小
console.log('\n📊 7. 验证构建大小...');
try {
  const sizeResult = execSync('du -sh dist', { encoding: 'utf-8' });
  const size = sizeResult.trim().split('\t')[0];
  
  // 解析大小（假设单位是M）
  const sizeNum = parseFloat(size);
  const unit = size.replace(sizeNum.toString(), '').trim();
  
  if (unit === 'M' && sizeNum > 50) {
    addResult('构建大小检查', 'WARN', `构建大小较大: ${size}`, '建议优化资源');
  } else {
    addResult('构建大小检查', 'PASS', `构建大小合理: ${size}`);
  }
} catch (error) {
  addResult('构建大小检查', 'WARN', '无法获取构建大小');
}

// 8. 生成验证报告
console.log('\n📊 8. 生成验证报告...');

const report = {
  timestamp: new Date().toISOString(),
  summary: {
    total: verificationResults.passed + verificationResults.failed + verificationResults.warnings,
    passed: verificationResults.passed,
    failed: verificationResults.failed,
    warnings: verificationResults.warnings,
    success: verificationResults.failed === 0
  },
  details: verificationResults.details,
  recommendations: []
};

// 生成建议
if (verificationResults.failed > 0) {
  report.recommendations.push('修复失败的验证项目');
}
if (verificationResults.warnings > 0) {
  report.recommendations.push('检查警告项目并考虑优化');
}
report.recommendations.push('在真实内网环境中进行功能测试');
report.recommendations.push('配置内网DNS和反向代理');

// 保存报告
fs.writeFileSync('intranet-verification-report.json', JSON.stringify(report, null, 2));

// 9. 输出总结
console.log('\n📈 验证总结:');
console.log(`• 总计: ${report.summary.total} 项检查`);
console.log(`• 通过: ${report.summary.passed} 项 ✅`);
console.log(`• 失败: ${report.summary.failed} 项 ❌`);
console.log(`• 警告: ${report.summary.warnings} 项 ⚠️`);

if (report.summary.success) {
  console.log('\n🎉 内网部署验证通过！');
  console.log('✅ 应用已准备好在内网环境中部署');
} else {
  console.log('\n⚠️ 内网部署验证未完全通过');
  console.log('❌ 请修复失败项目后重新验证');
}

console.log('\n📋 下一步操作:');
console.log('1. 查看详细报告: intranet-verification-report.json');
console.log('2. 修复失败和警告项目');
console.log('3. 在内网环境中部署测试');
console.log('4. 配置生产环境参数');

// 退出码
process.exit(report.summary.success ? 0 : 1);
