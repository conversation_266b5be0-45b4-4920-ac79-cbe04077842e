/**
 * Legacy Browser Polyfills
 * 为旧浏览器提供基本的 JavaScript 功能支持
 * 此文件放在 public 目录中，避免被 Vite 处理
 */

// 检测是否为旧浏览器
var isLegacyBrowser = function() {
  try {
    // 检测一些现代特性
    return !window.addEventListener || 
           !document.querySelector || 
           !Array.prototype.forEach ||
           !Object.keys ||
           typeof JSON === 'undefined';
  } catch (e) {
    return true;
  }
};

// 为旧浏览器添加基础 polyfills
var addBasicPolyfills = function() {
  // Array.prototype.forEach polyfill (IE8 及更早版本)
  if (!Array.prototype.forEach) {
    Array.prototype.forEach = function(callback, thisArg) {
      if (this == null) {
        throw new TypeError('Array.prototype.forEach called on null or undefined');
      }
      
      var T, k;
      var O = Object(this);
      var len = parseInt(O.length) || 0;
      
      if (typeof callback !== "function") {
        throw new TypeError(callback + ' is not a function');
      }
      
      if (arguments.length > 1) {
        T = thisArg;
      }
      
      k = 0;
      while (k < len) {
        var kValue;
        if (k in O) {
          kValue = O[k];
          callback.call(T, kValue, k, O);
        }
        k++;
      }
    };
  }

  // Array.prototype.indexOf polyfill (IE8 及更早版本)
  if (!Array.prototype.indexOf) {
    Array.prototype.indexOf = function(searchElement, fromIndex) {
      var k;
      if (this == null) {
        throw new TypeError('"this" is null or not defined');
      }

      var o = Object(this);
      var len = parseInt(o.length) || 0;
      
      if (len === 0) {
        return -1;
      }
      
      var n = parseInt(fromIndex) || 0;
      var k;
      
      if (n >= len) {
        return -1;
      }
      
      k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);
      
      while (k < len) {
        if (k in o && o[k] === searchElement) {
          return k;
        }
        k++;
      }
      return -1;
    };
  }

  // Object.keys polyfill (IE8 及更早版本)
  if (!Object.keys) {
    Object.keys = function(obj) {
      if (obj !== Object(obj)) {
        throw new TypeError('Object.keys called on a non-object');
      }
      var k = [], p;
      for (p in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, p)) {
          k.push(p);
        }
      }
      return k;
    };
  }

  // JSON polyfill (IE7 及更早版本)
  if (typeof JSON === 'undefined') {
    window.JSON = {
      parse: function(text) {
        try {
          return eval('(' + text + ')');
        } catch (e) {
          throw new Error('JSON parse error');
        }
      },
      stringify: function(obj) {
        if (obj === null) return 'null';
        if (typeof obj === 'undefined') return undefined;
        if (typeof obj === 'string') return '"' + obj.replace(/"/g, '\\"') + '"';
        if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);
        if (typeof obj === 'object') {
          if (obj instanceof Array) {
            var arr = [];
            for (var i = 0; i < obj.length; i++) {
              arr.push(JSON.stringify(obj[i]));
            }
            return '[' + arr.join(',') + ']';
          } else {
            var pairs = [];
            for (var key in obj) {
              if (obj.hasOwnProperty(key)) {
                pairs.push(JSON.stringify(key) + ':' + JSON.stringify(obj[key]));
              }
            }
            return '{' + pairs.join(',') + '}';
          }
        }
        return 'null';
      }
    };
  }

  // addEventListener polyfill (IE8 及更早版本)
  if (!window.addEventListener) {
    window.addEventListener = function(type, listener, useCapture) {
      if (window.attachEvent) {
        window.attachEvent('on' + type, listener);
      }
    };
    
    window.removeEventListener = function(type, listener, useCapture) {
      if (window.detachEvent) {
        window.detachEvent('on' + type, listener);
      }
    };
  }

  // querySelector polyfill (基础版本，仅支持简单选择器)
  if (!document.querySelector) {
    document.querySelector = function(selector) {
      // 简单的 ID 选择器
      if (selector.charAt(0) === '#') {
        return document.getElementById(selector.slice(1));
      }
      // 简单的类选择器
      if (selector.charAt(0) === '.') {
        var className = selector.slice(1);
        var elements = document.getElementsByTagName('*');
        for (var i = 0; i < elements.length; i++) {
          if (elements[i].className && elements[i].className.indexOf(className) !== -1) {
            return elements[i];
          }
        }
        return null;
      }
      // 标签选择器
      var elements = document.getElementsByTagName(selector);
      return elements.length > 0 ? elements[0] : null;
    };
  }

  // CustomEvent polyfill (IE9-11)
  if (typeof window.CustomEvent !== 'function') {
    function CustomEvent(event, params) {
      params = params || { bubbles: false, cancelable: false, detail: undefined };
      var evt = document.createEvent('CustomEvent');
      evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);
      return evt;
    }
    
    if (typeof window.Event !== 'undefined') {
      CustomEvent.prototype = window.Event.prototype;
    }
    
    window.CustomEvent = CustomEvent;
  }
};

// 创建兼容的浏览器检测函数
var createLegacyCompatibleBrowserCheck = function() {
  return {
    // 简化的浏览器信息检测
    getBrowserInfo: function() {
      var userAgent = navigator.userAgent.toLowerCase();
      var browser = {
        name: 'Unknown',
        version: 0,
        isLegacy: true
      };

      // 简单的浏览器检测
      if (userAgent.indexOf('msie') !== -1) {
        browser.name = 'Internet Explorer';
        var version = userAgent.match(/msie (\d+)/);
        browser.version = version ? parseInt(version[1]) : 0;
      } else if (userAgent.indexOf('firefox') !== -1) {
        browser.name = 'Firefox';
        var version = userAgent.match(/firefox\/(\d+)/);
        browser.version = version ? parseInt(version[1]) : 0;
      } else if (userAgent.indexOf('chrome') !== -1) {
        browser.name = 'Chrome';
        var version = userAgent.match(/chrome\/(\d+)/);
        browser.version = version ? parseInt(version[1]) : 0;
      } else if (userAgent.indexOf('safari') !== -1) {
        browser.name = 'Safari';
        var version = userAgent.match(/version\/(\d+)/);
        browser.version = version ? parseInt(version[1]) : 0;
      }

      return browser;
    },

    // 简化的兼容性检查
    checkCompatibility: function() {
      var browser = this.getBrowserInfo();
      var isCompatible = false;

      // 基本的版本检查
      if (browser.name === 'Internet Explorer' && browser.version >= 9) {
        isCompatible = true;
      } else if (browser.name === 'Chrome' && browser.version >= 30) {
        isCompatible = true;
      } else if (browser.name === 'Firefox' && browser.version >= 30) {
        isCompatible = true;
      } else if (browser.name === 'Safari' && browser.version >= 8) {
        isCompatible = true;
      }

      return {
        isCompatible: isCompatible,
        browser: browser,
        isLegacy: true,
        recommendations: isCompatible ? [] : [
          {
            type: 'warning',
            message: '您的浏览器版本较旧，建议升级以获得更好的体验',
            action: 'upgrade'
          }
        ]
      };
    },

    // 显示简单的升级提示
    showUpgradeNotification: function() {
      var notification = document.createElement('div');
      notification.className = 'browser-compat-notification legacy-browser';
      notification.innerHTML = [
        '<div style="background: #ffcc00; border: 2px solid #ff9900; padding: 10px; margin: 10px; font-family: Arial, sans-serif;">',
        '<strong>浏览器升级建议</strong><br>',
        '您的浏览器版本较旧，可能无法完全支持本网站的所有功能。<br>',
        '建议升级到最新版本的 Chrome、Firefox、Safari 或 Edge 浏览器。<br>',
        '<button onclick="this.parentNode.parentNode.style.display=\'none\'" style="margin-top: 5px; padding: 5px 10px; background: #0066cc; color: white; border: none; cursor: pointer;">我知道了</button>',
        '</div>'
      ].join('');

      // 添加到页面
      if (document.body) {
        document.body.appendChild(notification);
      } else {
        // 如果 body 还没有加载，等待加载完成
        var checkBody = function() {
          if (document.body) {
            document.body.appendChild(notification);
          } else {
            setTimeout(checkBody, 100);
          }
        };
        checkBody();
      }
    }
  };
};

// 初始化 polyfills
var initLegacySupport = function() {
  if (isLegacyBrowser()) {
    addBasicPolyfills();
    
    // 为旧浏览器创建全局兼容性检查器
    window.legacyBrowserCompat = createLegacyCompatibleBrowserCheck();
    
    // 添加 CSS 类标识
    if (document.documentElement) {
      document.documentElement.className += ' legacy-browser';
    }
    
    console.log('Legacy browser support initialized');
  }
};

// 在浏览器环境中直接初始化
if (typeof window !== 'undefined') {
  initLegacySupport();
  
  // 将函数暴露到全局作用域供其他脚本使用
  window.legacyBrowserSupport = {
    isLegacyBrowser: isLegacyBrowser,
    addBasicPolyfills: addBasicPolyfills,
    createLegacyCompatibleBrowserCheck: createLegacyCompatibleBrowserCheck,
    initLegacySupport: initLegacySupport
  };
}
