{"timestamp": "2025-07-19T18:58:29.723Z", "summary": {"total": 15, "passed": 14, "failed": 0, "warnings": 1, "success": true}, "details": [{"test": "构建文件检查", "status": "PASS", "message": "dist/index.html 存在", "details": null, "timestamp": "2025-07-19T18:58:29.684Z"}, {"test": "构建文件检查", "status": "PASS", "message": "dist/assets 存在", "details": null, "timestamp": "2025-07-19T18:58:29.684Z"}, {"test": "构建文件检查", "status": "PASS", "message": "dist/favicon.svg 存在", "details": null, "timestamp": "2025-07-19T18:58:29.684Z"}, {"test": "构建文件检查", "status": "PASS", "message": "dist/manifest.json 存在", "details": null, "timestamp": "2025-07-19T18:58:29.684Z"}, {"test": "构建文件检查", "status": "PASS", "message": "dist/sw.js 存在", "details": null, "timestamp": "2025-07-19T18:58:29.684Z"}, {"test": "外部链接检查", "status": "PASS", "message": "未发现有问题的外部链接", "details": null, "timestamp": "2025-07-19T18:58:29.719Z"}, {"test": "字体文件检查", "status": "PASS", "message": "fa-solid-900.woff2 存在", "details": null, "timestamp": "2025-07-19T18:58:29.720Z"}, {"test": "字体文件检查", "status": "PASS", "message": "fa-regular-400.woff2 存在", "details": null, "timestamp": "2025-07-19T18:58:29.720Z"}, {"test": "字体文件检查", "status": "PASS", "message": "fa-brands-400.woff2 存在", "details": null, "timestamp": "2025-07-19T18:58:29.720Z"}, {"test": "环境变量检查", "status": "PASS", "message": "VITE_INTRANET_MODE 已配置", "details": null, "timestamp": "2025-07-19T18:58:29.720Z"}, {"test": "环境变量检查", "status": "PASS", "message": "VITE_DISABLE_EXTERNAL_RESOURCES 已配置", "details": null, "timestamp": "2025-07-19T18:58:29.720Z"}, {"test": "环境变量检查", "status": "PASS", "message": "VITE_OFFLINE_MODE 已配置", "details": null, "timestamp": "2025-07-19T18:58:29.720Z"}, {"test": "Service Worker检查", "status": "PASS", "message": "Service Worker无外部依赖", "details": null, "timestamp": "2025-07-19T18:58:29.720Z"}, {"test": "依赖包检查", "status": "PASS", "message": "react-icons 已安装", "details": null, "timestamp": "2025-07-19T18:58:29.720Z"}, {"test": "构建大小检查", "status": "WARN", "message": "构建大小较大: 847M", "details": "建议优化资源", "timestamp": "2025-07-19T18:58:29.723Z"}], "recommendations": ["检查警告项目并考虑优化", "在真实内网环境中进行功能测试", "配置内网DNS和反向代理"]}