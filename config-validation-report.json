{"timestamp": "2025-07-19T08:05:27.558Z", "totalConfigs": 37, "validConfigs": 31, "missingRequired": 5, "ldapEnvironments": ["DEVVDI_ENV", "VDI_ENV"], "configStatus": {"NODE_ENV": {"status": "✅", "hasValue": true, "isRequired": false}, "CORS_ORIGIN": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAP_AUTH_PORT": {"status": "✅", "hasValue": true, "isRequired": false}, "VITE_LDAP_API_URL": {"status": "✅", "hasValue": true, "isRequired": false}, "VITE_CHAT_API_URL": {"status": "🔶", "hasValue": false, "isRequired": false}, "VITE_LITELLM_API_BASE": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAP_DEFAULT_HOST": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAP_DEFAULT_PORT": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAP_DEFAULT_ENVIRONMENT": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAP_CONNECTION_TIMEOUT": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAP_SEARCH_TIMEOUT": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAP_MAX_RETRIES": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAP_240.10云桌面环境_URL": {"status": "❌", "hasValue": false, "isRequired": true}, "LDAP_240.10云桌面环境_BASE_DN": {"status": "❌", "hasValue": false, "isRequired": true}, "LDAP_242.2云桌面环境_URL": {"status": "❌", "hasValue": false, "isRequired": true}, "LDAP_242.2云桌面环境_BASE_DN": {"status": "❌", "hasValue": false, "isRequired": true}, "LDAP_TEST_PORT": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAPS_TEST_PORT": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAP_GC_PORT": {"status": "✅", "hasValue": true, "isRequired": false}, "LDAPS_GC_PORT": {"status": "✅", "hasValue": true, "isRequired": false}, "LITELLM_API_BASE": {"status": "❌", "hasValue": false, "isRequired": true}, "LITELLM_MASTER_KEY": {"status": "✅", "hasValue": true, "isRequired": true}, "OPENAI_API_KEY": {"status": "✅", "hasValue": true, "isRequired": false}, "OPENAI_MODEL": {"status": "✅", "hasValue": true, "isRequired": false}, "OPENAI_BASE_URL": {"status": "✅", "hasValue": true, "isRequired": false}, "ANTHROPIC_API_KEY": {"status": "✅", "hasValue": true, "isRequired": false}, "ANTHROPIC_MODEL": {"status": "✅", "hasValue": true, "isRequired": false}, "AZURE_OPENAI_API_KEY": {"status": "✅", "hasValue": true, "isRequired": false}, "AZURE_OPENAI_ENDPOINT": {"status": "✅", "hasValue": true, "isRequired": false}, "ENABLE_OPENAI": {"status": "✅", "hasValue": true, "isRequired": false}, "ENABLE_ANTHROPIC": {"status": "✅", "hasValue": true, "isRequired": false}, "ENABLE_AZURE": {"status": "✅", "hasValue": true, "isRequired": false}, "LLM_TIMEOUT": {"status": "✅", "hasValue": true, "isRequired": false}, "LLM_MAX_TOKENS": {"status": "✅", "hasValue": true, "isRequired": false}, "LLM_TEMPERATURE": {"status": "✅", "hasValue": true, "isRequired": false}, "API_RETRY_COUNT": {"status": "✅", "hasValue": true, "isRequired": false}, "API_RETRY_DELAY": {"status": "✅", "hasValue": true, "isRequired": false}}}